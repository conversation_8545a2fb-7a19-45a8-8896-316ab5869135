import RightContent from '@/components/RightContent';
import S2Icon, { ICON } from '@/components/S2Icon';
import { Space, Spin, ConfigProvider } from 'antd';
import ScaleLoader from 'react-spinners/ScaleLoader';
import { history, RunTimeLayoutConfig } from '@umijs/max';
import defaultSettings from '../config/defaultSettings';
import settings from '../config/themeSettings';
import { queryCurrentUser } from './services/user';
import { deleteUrlQuery, isMobile, getToken } from '@/utils/utils';
import { publicPath } from '../config/defaultSettings';
import type { DefaultSetting } from '../config/defaultSettings';
// import { Copilot } from 'hchatdata-chat-sdk';
import { configProviderTheme } from '../config/themeSettings';
export { request } from './services/request';
import { BASE_TITLE } from '@/common/constants';
import { ROUTE_AUTH_CODES } from '../config/routes';
import AppPage from './pages/index';
import avatar from './assets/ai-avator.png';

// import logo from '@/assets/icon/logo.png';
// import aiBot from '@/assets/aiBot.svg';
// import AIBotIcon from './assets/AIBotIcon';

// 过滤第三方库的findDOMNode警告
if (process.env.NODE_ENV === 'development') {
  const originalWarn = console.warn;
  console.warn = (...args) => {
    if (args[0] && typeof args[0] === 'string' && args[0].includes('findDOMNode is deprecated')) {
      return; // 忽略findDOMNode警告
    }
    originalWarn.apply(console, args);
  };
}

const replaceRoute = '/';

const getRunningEnv = async () => {
  try {
    const response = await fetch(`${publicPath}hchatdata.config.json`);
    const config = await response.json();
    return config;
  } catch (error) {
    console.warn('无法获取配置文件: 运行时环境将以semantic启动');
  }
};

Spin.setDefaultIndicator(
  <ScaleLoader color={settings['primary-color']} height={25} width={2} radius={2} margin={2} />,
);

const getAuthCodes = (params: any) => {
  const { currentUser } = params;
  const codes = [];
  if (currentUser?.superAdmin) {
    codes.push(ROUTE_AUTH_CODES.SYSTEM_ADMIN);
  }
  if (currentUser?.isAdmin === 1) {
    codes.push(ROUTE_AUTH_CODES.ADMIN);
  }
  return codes;
};

// 判断用户是否有管理员权限
const hasAdminPermission = (authCodes: string[]) => {
  return authCodes.includes(ROUTE_AUTH_CODES.ADMIN) || authCodes.includes(ROUTE_AUTH_CODES.SYSTEM_ADMIN);
};

export async function getInitialState(): Promise<{
  settings?: DefaultSetting;
  currentUser?: API.CurrentUser;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
  codeList?: string[];
  authCodes?: string[];
  systemMode?: 'frontend' | 'backend'; // 系统模式：前台或后台
  hasAdminPermission?: boolean; // 是否有管理员权限
}> {
  const fetchUserInfo = async () => {
    try {
      const { code, data } = await queryCurrentUser();
      if (code === 200) {
        return { ...data, staffName: data.staffName || data.name };
      }
    } catch (error) {}
    return undefined;
  };

  let currentUser: any;
  if (!window.location.pathname.includes('login')) {
    currentUser = await fetchUserInfo();
  }

  if (currentUser) {
    localStorage.setItem('user', currentUser.staffName);
    if (currentUser.orgName) {
      localStorage.setItem('organization', currentUser.orgName);
    }
  }

  const authCodes = getAuthCodes({
    currentUser,
  });

  const hasAdminPerm = hasAdminPermission(authCodes);

  // 从localStorage获取系统模式，默认为前台模式
  const savedSystemMode = localStorage.getItem('systemMode') as 'frontend' | 'backend' | null;
  const systemMode = hasAdminPerm ? (savedSystemMode || 'frontend') : 'frontend';

  return {
    fetchUserInfo,
    currentUser,
    settings: defaultSettings,
    authCodes,
    systemMode,
    hasAdminPermission: hasAdminPerm,
  };
}

// export async function patchRoutes({ routes }) {
//   const config = await getRunningEnv();
//   if (config && config.env) {
//     window.RUNNING_ENV = config.env;
//     const { env } = config;
//     const target = routes[0].routes;
//     if (env) {
//       const envRoutes = traverseRoutes(target, env);
//       // 清空原本route;
//       target.splice(0, 99);
//       // 写入根据环境转换过的的route
//       target.push(...envRoutes);
//     }
//   } else {
//     const target = routes[0].routes;
//     // start-standalone模式不存在env，在此模式下不显示chatSetting
//     const envRoutes = target.filter((item: any) => {
//       return !['chatSetting'].includes(item.name);
//     });
//     target.splice(0, 99);
//     target.push(...envRoutes);
//   }
// }

export function onRouteChange() {
  setTimeout(() => {
    let title = window.document.title === "Supersonic"? "AIData": window.document.title;
    console.log("window.document.title=",window.document.title)
    if (!title.toLowerCase().endsWith(BASE_TITLE.toLowerCase())) {
      window.document.title = `${title}-${BASE_TITLE}`;
    }
  }, 100);
}

export const layout: RunTimeLayoutConfig = (params) => {
  const { initialState } = params as any;

  // 菜单过滤函数
  const menuDataRender = (menuData: any[]) => {
    const { systemMode, hasAdminPermission, lastModeChangeTime } = initialState || {};

    // 使用lastModeChangeTime确保状态变化时重新渲染
    const currentSystemMode = systemMode || 'frontend';

    if (!hasAdminPermission) {
      // 非管理员用户只显示智慧问答
      return menuData.filter(item => item.name === 'chat');
    }

    if (currentSystemMode === 'frontend') {
      // 前台模式：只显示智慧问答
      return menuData.filter(item => item.name === 'chat');
    } else {
      // 后台模式：显示管理功能，隐藏智慧问答
      return menuData.filter(item => item.name !== 'chat');
    }
  };

  return {
    onMenuHeaderClick: (e) => {
      e.preventDefault();
      history.push(replaceRoute);
    },
    menuDataRender,
    // 使用key来强制重新渲染，当系统模式改变时
    key: `layout-${initialState?.systemMode || 'frontend'}-${initialState?.lastModeChangeTime || 0}`,
    logo: (
      <Space>
        {/*<S2Icon*/}
        {/*  icon={ICON.iconlogobiaoshi}*/}
        {/*  size={30}*/}
        {/*  color="#1672fa"*/}
        {/*  style={{ display: 'inline-block', marginTop: 8 }}*/}
        {/*/>*/}
        <div style={{ display: 'flex', alignItems: 'center', marginLeft: '30px' }}>
            {/* <AIBotIcon /> */}
            {/* <svg 
              viewBox="0 0 1024 1024" 
              version="1.1" 
              xmlns="http://www.w3.org/2000/svg" 
              width="26" 
              height="26"
              style={{marginRight: '10px',fill: '#0057FF'}}
            >
              <path 
                d="M802.367499 7.231995c26.751983 17.087989 23.231985 48.12797-10.495994 93.119942l-59.263963 85.631947a449.791719 449.791719 0 0 1 189.887881 210.239868A63.99996 63.99996 0 0 1 1023.99936 447.99972v383.93576a63.99996 63.99996 0 0 1-127.99992 0v-25.087984A447.74372 447.74372 0 0 1 511.99968 1023.99936a447.74372 447.74372 0 0 1-383.99976-217.087864V831.99948a63.99996 63.99996 0 0 1-127.99992 0V447.99972a63.99996 63.99996 0 0 1 101.567937-51.839968 449.471719 449.471719 0 0 1 181.375886-205.247871l-62.719961-90.559944C186.495883 55.359965 182.975886 24.319985 209.727869 7.231995c26.751983-17.087989 54.335966-3.839998 82.687948 39.935976l74.495954 104.831934A447.42372 447.42372 0 0 1 511.99968 127.99992c47.23197 0 92.735942 7.295995 135.487915 20.863987l72.191955-101.759936c28.351982-43.711973 55.935965-56.959964 82.687949-39.871976zM511.99968 191.99988a383.99976 383.99976 0 0 0-383.99976 381.119762v5.759996l0.256 11.519993A383.99976 383.99976 0 1 0 511.99968 191.99988z m-39.679975 163.711898L640.959599 767.99952h-62.143961l-48.12797-124.863922H358.399776L313.087804 767.99952h-57.919963L413.439742 355.711778h58.751963z m276.159827 0V767.99952h-54.591966V355.711778h54.591966zM441.599724 398.975751a525.567672 525.567672 0 0 1-22.207986 78.71995l-45.247972 120.959925H513.919679L470.847706 484.479697A1128.383295 1128.383295 0 0 1 441.599724 398.975751z" 
              />
            </svg> */}
            <img
              src={avatar}
              width={40}
              height={40}
              style={{
                display: 'inline-block',
                objectFit: 'contain'
              }}
              alt="logo-image"
            />
            <div className="ai-logo">
              <span className="ai-text">AI</span>
              <span className="data-text">Data</span>
            </div>
        </div>
      </Space>
    ),
    contentStyle: { ...(initialState?.contentStyle || {}) },
    rightContentRender: () => <RightContent />,
    disableContentMargin: true,
    // menuHeaderRender: undefined,
    childrenRender: (dom) => {
      return (
        <ConfigProvider theme={configProviderTheme}>
          <div
            style={{
              // 修改为与main元素一致的高度，避免留白和滚动问题
              height: 'calc(100vh -66px)',
              overflow: 'auto',
            }}
          >
            {/* <AppPage dom={dom} /> */}
            {dom}
            {/* {history.location.pathname !== '/chat' && !isMobile && (
              <Copilot token={getToken() || ''} isDeveloper />
            )} */}
          </div>
        </ConfigProvider>
      );
    },
    ...initialState?.settings,
  };
};
