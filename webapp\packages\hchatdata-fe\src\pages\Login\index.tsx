// import type { FC } from 'react';
import styles from './style.less';
import { Button, Form, Input, message, Space } from 'antd';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import RegisterForm from './components/RegisterForm';
// import ForgetPwdForm from './components/ForgetPwdForm';
import { ROUTE_AUTH_CODES } from '../../../config/routes';
// import S2Icon, { ICON } from '@/components/S2Icon';
import React, { useState } from 'react';
import { useForm } from 'antd/lib/form/Form';
import type { RegisterFormDetail } from './components/types';
import { postUserLogin, userRegister } from './services';
import { AUTH_TOKEN_KEY } from '@/common/constants';
import { queryCurrentUser } from '@/services/user';
import { history, useModel } from '@umijs/max';
import CryptoJS from 'crypto-js';
import { encryptPassword } from '@/utils/utils';
import { preloadCriticalRoutes, preloadRoutesByRole } from '@/utils/routePreloader';

// 判断用户是否有管理员权限
const hasAdminPermission = (authCodes: string[]) => {
  return authCodes.includes(ROUTE_AUTH_CODES.ADMIN) || authCodes.includes(ROUTE_AUTH_CODES.SYSTEM_ADMIN);
};

// import logo from '@/assets/icon/logo.png';

const { Item } = Form;
const LoginPage: React.FC = () => {
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  const [loading,setLoading] = useState<boolean>(false);
  const encryptKey = CryptoJS.enc.Utf8.parse('hchatdata@2024');
  const [form] = useForm();
  const { initialState = {}, setInitialState } = useModel('@@initialState');
  // 通过用户信息进行登录
  const loginDone = async (values: RegisterFormDetail) => {
    const { code, data, msg } = await postUserLogin(values);
    if (code === 200) {
      localStorage.setItem(AUTH_TOKEN_KEY, data);
      const { code: queryUserCode, data: queryUserData } = await queryCurrentUser();
      if (queryUserCode === 200) {
        const currentUser = {
          ...queryUserData,
          staffName: queryUserData.staffName || queryUserData.name,
        };
        const authCodes = Array.isArray(initialState?.authCodes) ? initialState?.authCodes : [];
        if (queryUserData.superAdmin) {
          authCodes.push(ROUTE_AUTH_CODES.SYSTEM_ADMIN);
          authCodes.push(ROUTE_AUTH_CODES.ADMIN);
        }
        if (queryUserData.isAdmin === 1) {
          authCodes.push(ROUTE_AUTH_CODES.ADMIN);
        }

        const hasAdminPerm = hasAdminPermission(authCodes);

        // 从localStorage获取系统模式，默认为前台模式
        const savedSystemMode = localStorage.getItem('systemMode') as 'frontend' | 'backend' | null;
        const systemMode = hasAdminPerm ? (savedSystemMode || 'frontend') : 'frontend';

        setInitialState({
          ...initialState,
          currentUser,
          authCodes,
          hasAdminPermission: hasAdminPerm,
          systemMode
        });

        // 登录成功后立即开始预加载关键路由
        preloadCriticalRoutes().catch(error => {
          console.warn('预加载关键路由失败:', error);
        });

        // 根据用户角色预加载特定路由
        preloadRoutesByRole(authCodes).catch(error => {
          console.warn('预加载角色路由失败:', error);
        });
      }
      history.push('/');
      return;
    } else {
      message.error(msg);
    }
  };

  // 处理登录按钮响应
  const handleLogin = async () => {
    setLoading(true);
    const { validateFields } = form;
    try{
      const content = await validateFields();
      await loginDone({ ...content, password: encryptPassword(content.password, encryptKey) });
    }catch(error){
      console.log('Failed:', error);
    }finally{
      setLoading(false);
    }
  };

  // 处理注册弹窗确定按钮
  const handleRegister = async (values: RegisterFormDetail) => {
    const enCodeValues = { ...values, password: encryptPassword(values.password, encryptKey) };
    const { code, msg } = await userRegister(enCodeValues);
    if (code === 200) {
      message.success('注册成功');
      setCreateModalVisible(false);
      // 注册完自动帮用户登录
      await loginDone(enCodeValues);
    } else {
      message.error(msg);
    }
  };

  // 相应注册按钮
  const handleRegisterBtn = () => {
    setCreateModalVisible(true);
  };

  // // 忘记密码弹窗确定响应
  // const handleForgetPwd = async (values: RegisterFormDetail) => {
  //   await getUserForgetPwd({ ...values });
  //   message.success('发送邮件成功，请在收到邮件后进入邮件链接进行密码重置');
  //   setForgetModalVisible(false);
  // };

  // // 响应忘记密码按钮
  // const handleForgetPwdBtn = () => {
  //   setForgetModalVisible(true);
  // };

  return (
    <div className={styles.loginWarp}>
      <div className={styles.content}>
        <div className={styles.formContent}>
          <div className={styles.formBox}>
            <Form form={form} labelCol={{ span: 6 }} colon={false}>
              <div className={styles.loginMain}>
                <h3 className={styles.title}>
                  <Space align="center">
                    <div>AIData</div>
                  </Space>
                </h3>
                <Item name="name" rules={[{ required: true }]} label="">
                  <Input size="large" placeholder="用户名" prefix={<UserOutlined />} />
                </Item>
                <Item name="password" rules={[{ required: true }]} label="">
                  <Input
                    size="large"
                    type="password"
                    placeholder="密码"
                    onPressEnter={handleLogin}
                    prefix={<LockOutlined />}
                  />
                </Item>

                {
                  loading ? (
                    <Button className={styles.signInBtn} type="primary" style={{opacity: 0.7}}>
                      登录中...
                    </Button>
                  ):(
                    <Button className={styles.signInBtn} type="primary" onClick={handleLogin}>
                      登录
                    </Button>
                  )
                }

                {/* <div className={styles.tool}>
                  <Button className={styles.button} onClick={handleRegisterBtn}>
                    注册
                  </Button>
                </div> */}
              </div>
            </Form>
          </div>
        </div>
      </div>
      <RegisterForm
        onCancel={() => {
          setCreateModalVisible(false);
        }}
        onSubmit={handleRegister}
        createModalVisible={createModalVisible}
      />
    </div>
  );
};

export default LoginPage;
