import { useState, useCallback } from 'react';
import { useModel, history } from '@umijs/max';
import { SystemMode, getDefaultRedirectPath } from '@/utils/systemModeUtils';

export default function useSystemModeModel() {
  const { initialState, setInitialState } = useModel('@@initialState');
  
  // 切换系统模式
  const toggleSystemMode = useCallback(() => {
    if (!initialState?.hasAdminPermission) {
      return; // 没有管理员权限不能切换
    }

    const currentMode = initialState.systemMode || 'frontend';
    const newMode: SystemMode = currentMode === 'frontend' ? 'backend' : 'frontend';

    // 保存到localStorage
    localStorage.setItem('systemMode', newMode);

    // 更新全局状态
    setInitialState({
      ...initialState,
      systemMode: newMode,
      // 添加一个时间戳来强制重新渲染
      lastModeChangeTime: Date.now(),
    });

    // 根据新模式重定向页面
    const redirectPath = getDefaultRedirectPath(newMode);

    // 使用setTimeout确保状态更新后再跳转
    setTimeout(() => {
      history.push(redirectPath);
      // 强制刷新页面以确保菜单正确更新
      window.location.reload();
    }, 100);
  }, [initialState, setInitialState]);

  // 设置系统模式
  const setSystemMode = useCallback((mode: SystemMode) => {
    if (!initialState?.hasAdminPermission) {
      return; // 没有管理员权限不能切换
    }

    // 保存到localStorage
    localStorage.setItem('systemMode', mode);

    // 更新全局状态
    setInitialState({
      ...initialState,
      systemMode: mode,
      // 添加一个时间戳来强制重新渲染
      lastModeChangeTime: Date.now(),
    });

    // 根据新模式重定向页面
    const redirectPath = getDefaultRedirectPath(mode);

    // 使用setTimeout确保状态更新后再跳转
    setTimeout(() => {
      history.push(redirectPath);
      // 强制刷新页面以确保菜单正确更新
      window.location.reload();
    }, 100);
  }, [initialState, setInitialState]);

  return {
    systemMode: initialState?.systemMode || 'frontend',
    hasAdminPermission: initialState?.hasAdminPermission || false,
    toggleSystemMode,
    setSystemMode,
  };
}
