import { useState, useCallback } from 'react';
import { useModel, history } from '@umijs/max';
import { SystemMode, getDefaultRedirectPath } from '@/utils/systemModeUtils';

export default function useSystemModeModel() {
  const { initialState, setInitialState } = useModel('@@initialState');
  
  // 切换系统模式
  const toggleSystemMode = useCallback(() => {
    if (!initialState?.hasAdminPermission) {
      return; // 没有管理员权限不能切换
    }

    const currentMode = initialState.systemMode || 'frontend';
    const newMode: SystemMode = currentMode === 'frontend' ? 'backend' : 'frontend';

    // 保存到localStorage
    localStorage.setItem('systemMode', newMode);

    // 更新全局状态
    setInitialState({
      ...initialState,
      systemMode: newMode,
    });

    // 根据新模式重定向页面
    const redirectPath = getDefaultRedirectPath(newMode);
    history.push(redirectPath);
  }, [initialState, setInitialState]);

  // 设置系统模式
  const setSystemMode = useCallback((mode: SystemMode) => {
    if (!initialState?.hasAdminPermission) {
      return; // 没有管理员权限不能切换
    }

    // 保存到localStorage
    localStorage.setItem('systemMode', mode);

    // 更新全局状态
    setInitialState({
      ...initialState,
      systemMode: mode,
    });

    // 根据新模式重定向页面
    const redirectPath = getDefaultRedirectPath(mode);
    history.push(redirectPath);
  }, [initialState, setInitialState]);

  return {
    systemMode: initialState?.systemMode || 'frontend',
    hasAdminPermission: initialState?.hasAdminPermission || false,
    toggleSystemMode,
    setSystemMode,
  };
}
